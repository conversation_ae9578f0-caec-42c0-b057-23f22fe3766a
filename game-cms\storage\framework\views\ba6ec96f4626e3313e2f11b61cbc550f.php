<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Game CMS</title>
    <link href="<?php echo e(asset('css/app.css')); ?>" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #3b82f6;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        .content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .game-list {
            list-style: none;
            padding: 0;
        }
        .game-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .game-item:last-child {
            border-bottom: none;
        }
        .nav-links {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .btn {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Game CMS Dashboard</h1>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <h2>System Overview</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($stats['total_games'] ?? 0); ?></div>
                    <div class="stat-label">Total Games</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($stats['total_categories'] ?? 0); ?></div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($stats['total_plays'] ?? 0); ?></div>
                    <div class="stat-label">Total Game Plays</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($stats['plays_today'] ?? 0); ?></div>
                    <div class="stat-label">Plays Today</div>
                </div>
            </div>
            
            <h3>Most Popular Games</h3>
            <ul class="game-list">
                <?php $__empty_1 = true; $__currentLoopData = $popularGames ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li class="game-item">
                        <span><?php echo e($game->name); ?></span>
                        <span><?php echo e($game->play_count); ?> plays</span>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li class="game-item">No games available</li>
                <?php endif; ?>
            </ul>
            
            <div class="nav-links">
                <a href="<?php echo e(route('welcome')); ?>" class="btn">Go to Website</a>
                <a href="<?php echo e(url('/admin')); ?>" class="btn">Admin Panel</a>
            </div>
        </div>
    </div>
</body>
</html><?php /**PATH D:\cms-theme2\game-cms\resources\views/dashboard.blade.php ENDPATH**/ ?>