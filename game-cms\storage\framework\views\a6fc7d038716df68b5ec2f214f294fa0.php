<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Game CMS - Welcome</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Styles / Scripts -->
    <?php if(file_exists(public_path('build/manifest.json'))): ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php else: ?>
        <style>
            /* Fallback styles */
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 2rem;
            }
            
            .hero {
                text-align: center;
                padding: 4rem 0;
            }
            
            .hero h1 {
                font-size: 3.5rem;
                font-weight: 700;
                margin-bottom: 1rem;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            
            .hero p {
                font-size: 1.25rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }
            
            .btn {
                display: inline-block;
                padding: 0.75rem 1.5rem;
                margin: 0.5rem;
                background: rgba(255,255,255,0.2);
                color: white;
                text-decoration: none;
                border-radius: 8px;
                border: 2px solid rgba(255,255,255,0.3);
                transition: all 0.3s ease;
                font-weight: 500;
            }
            
            .btn:hover {
                background: rgba(255,255,255,0.3);
                border-color: rgba(255,255,255,0.5);
                transform: translateY(-2px);
            }
            
            .btn-primary {
                background: rgba(255,255,255,0.9);
                color: #667eea;
                border-color: white;
            }
            
            .btn-primary:hover {
                background: white;
                color: #667eea;
            }
            
            .features {
                margin: 4rem 0;
            }
            
            .features h2 {
                text-align: center;
                font-size: 2.5rem;
                margin-bottom: 3rem;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                margin-top: 2rem;
            }
            
            .feature-card {
                background: rgba(255,255,255,0.1);
                padding: 2rem;
                border-radius: 12px;
                border: 1px solid rgba(255,255,255,0.2);
                backdrop-filter: blur(10px);
                transition: transform 0.3s ease;
            }
            
            .feature-card:hover {
                transform: translateY(-5px);
                background: rgba(255,255,255,0.15);
            }
            
            .feature-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }
            
            .feature-card h3 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
                font-weight: 600;
            }
            
            .feature-card p {
                opacity: 0.9;
                line-height: 1.6;
            }
            
            .footer {
                text-align: center;
                margin-top: 4rem;
                padding: 2rem 0;
                border-top: 1px solid rgba(255,255,255,0.2);
            }
            
            .nav-buttons {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }
            
            .nav-btn {
                padding: 8px 16px;
                background: rgba(255,255,255,0.9);
                color: #333;
                text-decoration: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.3);
            }
            
            .nav-btn:hover {
                background: white;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
            
            @media (max-width: 768px) {
                .hero h1 {
                    font-size: 2.5rem;
                }
                
                .feature-grid {
                    grid-template-columns: 1fr;
                }
                
                .nav-buttons {
                    position: static;
                    justify-content: center;
                    margin-bottom: 2rem;
                }
            }
        </style>
    <?php endif; ?>
</head>
<body>
    <div class="nav-buttons">
        <a href="<?php echo e(route('home')); ?>" class="nav-btn">🎮 Games</a>
        <a href="<?php echo e(route('dashboard')); ?>" class="nav-btn">📊 Dashboard</a>
        <a href="<?php echo e(url('/admin')); ?>" class="nav-btn">⚙️ Admin</a>
    </div>
    
    <div class="container">
        <div class="hero">
            <h1>🎮 Game CMS</h1>
            <p>A powerful content management system for HTML5 games</p>
            <div>
                <a href="<?php echo e(route('home')); ?>" class="btn btn-primary">Browse Games</a>
                <a href="<?php echo e(route('dashboard')); ?>" class="btn">Dashboard</a>
                <a href="<?php echo e(url('/admin')); ?>" class="btn">Admin Panel</a>
            </div>
        </div>
        
        <div class="features">
            <h2>✨ Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>Game Management</h3>
                    <p>Easily manage your HTML5 games with categories, descriptions, and play tracking.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Analytics</h3>
                    <p>Track game popularity, play counts, and user engagement with built-in analytics.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Customizable</h3>
                    <p>Fully customizable themes and layouts to match your brand and style.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Fast & Responsive</h3>
                    <p>Built with Laravel and optimized for speed and mobile responsiveness.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>Admin Panel</h3>
                    <p>Powerful Filament-based admin panel for easy content management.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>SEO Optimized</h3>
                    <p>Built-in SEO features with meta tags, sitemaps, and search engine optimization.</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>
                <strong>Laravel <?php echo e(app()->version()); ?></strong> • 
                <strong>PHP <?php echo e(PHP_VERSION); ?></strong>
            </p>
            <p style="margin-top: 1rem; opacity: 0.7;">
                <a href="https://laravel.com/docs" style="color: white; text-decoration: underline;">Documentation</a> • 
                <a href="https://github.com" style="color: white; text-decoration: underline;">GitHub</a>
            </p>
        </div>
    </div>
</body>
</html>
<?php /**PATH D:\cms-theme2\game-cms\resources\views/welcome.blade.php ENDPATH**/ ?>