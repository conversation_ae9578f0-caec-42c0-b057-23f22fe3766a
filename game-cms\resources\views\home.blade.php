@extends('layouts.app')

@section('title', 'Geometry Dash Full Version - Play Online Free')

@section('meta_description', 'Play Geometry Dash Full Version and other free HTML5 games. Enjoy the best online gaming experience with optimized games for all devices.')

@section('schema_markup')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ App\Models\Setting::get('site_name', 'Geometry Dash Full Version') }}",
    "url": "{{ url('/') }}",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ url('/search?q={search_term_string}') }}",
        "query-input": "required name=search_term_string"
    }
}
</script>
@endsection

@section('content')
<!-- Main Game Section -->
<div class="main-game-container">
    <div class="game-wrapper">
        <div class="game-frame" id="gameFrame">
            <div class="game-placeholder" id="gamePlaceholder">
                <div class="play-button" id="playButton">
                    <span class="play-icon">▶</span>
                    <span class="play-text">PLAY</span>
                </div>
            </div>
            <iframe
                id="gameIframe"
                src="https://geometrydash.io/geometry-dash-full-version/"
                style="display: none;"
                allowfullscreen>
            </iframe>
            <div class="loading-screen" id="loadingScreen" style="display: none;">
                <div class="spinner"></div>
                <p>Loading...</p>
            </div>
            <button class="fullscreen-btn" id="fullscreenBtn" style="display: none;">
                <span>⛶</span>
            </button>
        </div>
    </div>
</div>

<!-- Content Section -->
<div class="content-section">
    <div class="container">
        <div class="content-grid">
            <!-- Game Description -->
            <div class="game-description">
                <h1>Geometry Dash Full Version</h1>
                <div class="game-meta">
                    <div class="game-tags">
                        <span class="tag">Action</span>
                        <span class="tag">Arcade</span>
                        <span class="tag">Platform</span>
                    </div>
                    <div class="game-rating">
                        <span class="stars">⭐⭐⭐⭐⭐</span>
                        <span class="rating-text">4.8/5</span>
                    </div>
                </div>
                <div class="game-description-text">
                    <p>Geometry Dash is a rhythm-based action platformer game where you control a geometric shape through challenging levels filled with obstacles. Jump, fly, and flip your way through dangerous passages and spiky obstacles in this addictive game!</p>
                    <p>Features include multiple game modes, custom soundtracks, level editor, and online level sharing. Test your skills and reflexes in this ultimate platformer challenge!</p>
                </div>
            </div>

            <!-- Popular Games Sidebar -->
            <div class="popular-games-sidebar">
                <h3>🔥 Most Played Games</h3>
                <div class="popular-games-list">
                    @foreach($popularGames->take(6) as $game)
                    <div class="popular-game-item">
                        <div class="game-thumbnail">
                            @if($game->avatar)
                                <img src="{{ asset('storage/' . $game->avatar) }}" alt="{{ $game->name }}">
                            @else
                                <div class="placeholder-thumb">🎮</div>
                            @endif
                        </div>
                        <div class="game-info">
                            <h4>{{ $game->name }}</h4>
                            <p>{{ number_format($game->play_count) }} plays</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
<!-- How to Play & Comments Section -->
<div class="secondary-content">
    <div class="container">
        <div class="content-grid">
            <!-- How to Play -->
            <div class="how-to-play">
                <h3>🎮 How to Play</h3>
                <div class="instructions">
                    <ul>
                        <li><strong>SPACE/UP Arrow:</strong> Jump</li>
                        <li><strong>HOLD DOWN:</strong> Multi-jump</li>
                        <li><strong>UP Arrow (Ship mode):</strong> Fly up</li>
                        <li><strong>UP Arrow (Ball mode):</strong> Switch gravity</li>
                        <li><strong>UP Arrow (UFO mode):</strong> Jump higher</li>
                        <li><strong>UP Arrow (Wave mode):</strong> Control movement</li>
                    </ul>
                    <p><strong>Goal:</strong> Navigate through each level by jumping, flying and flipping your way to the end. Avoid hitting any obstacles or you'll have to start over!</p>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="comments-section">
                <h3>💬 Comments</h3>
                <div class="comment-form">
                    <textarea placeholder="Share your thoughts about this game..."></textarea>
                    <button class="submit-comment">Post Comment</button>
                </div>
                <div class="comments-list">
                    <div class="comment">
                        <div class="comment-author">Player123</div>
                        <div class="comment-text">Amazing game! The music sync is perfect!</div>
                        <div class="comment-time">2 hours ago</div>
                    </div>
                    <div class="comment">
                        <div class="comment-author">GamerPro</div>
                        <div class="comment-text">Level 3 is so hard but addictive!</div>
                        <div class="comment-time">5 hours ago</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Recent Games Section -->
<div class="recent-games-section">
    <div class="container">
        <h2>🆕 Recently Added Games</h2>
        <div class="games-grid">
            @foreach($newGames->take(12) as $game)
            <div class="game-card">
                <div class="game-thumbnail">
                    @if($game->avatar)
                        <img src="{{ asset('storage/' . $game->avatar) }}" alt="{{ $game->name }}">
                    @else
                        <div class="placeholder-thumb">🎮</div>
                    @endif
                </div>
                <div class="game-info">
                    <h4>{{ $game->name }}</h4>
                    <p class="game-category">{{ $game->categories->first()->name ?? 'Game' }}</p>
                    <div class="game-stats">
                        <span>⭐ 4.0</span>
                        <span>👥 {{ number_format($game->play_count) }}</span>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
<style>
/* Main Game Styles */
.main-game-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
}

.game-wrapper {
    max-width: 1000px;
    width: 100%;
    padding: 0 20px;
}

.game-frame {
    position: relative;
    width: 100%;
    height: 600px;
    background: #000;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    overflow: hidden;
}

.game-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.play-button {
    background: linear-gradient(45deg, #ff6b35, #ff8e53);
    border: none;
    border-radius: 50px;
    padding: 20px 40px;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

.play-button:hover {
    background: linear-gradient(45deg, #ff8e53, #ffab7a);
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.6);
}

.play-icon {
    font-size: 30px;
}

#gameIframe {
    width: 100%;
    height: 100%;
    border: none;
}

.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #333;
    border-top: 5px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fullscreen-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 18px;
}

/* Content Styles */
.content-section, .secondary-content {
    padding: 40px 0;
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.game-description h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 20px;
}

.game-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.game-tags {
    display: flex;
    gap: 10px;
}

.tag {
    background: #007bff;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.game-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.popular-games-sidebar h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.popular-game-item {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.game-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.game-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-thumb {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

/* Recent Games */
.recent-games-section {
    padding: 60px 0;
    background: white;
}

.recent-games-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 40px;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.game-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.game-card:hover {
    transform: translateY(-5px);
}

.game-card .game-thumbnail {
    width: 100%;
    height: 150px;
}

.game-card .game-info {
    padding: 15px;
}

.game-card h4 {
    margin-bottom: 5px;
    color: #333;
}

.game-category {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.game-stats {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #888;
}

/* Comments */
.comments-section h3, .how-to-play h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.comment-form textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    resize: vertical;
    min-height: 100px;
    margin-bottom: 10px;
}

.submit-comment {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.comment {
    background: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.comment-author {
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.comment-time {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

/* How to Play */
.instructions ul {
    list-style: none;
    padding: 0;
}

.instructions li {
    background: white;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .game-frame {
        height: 400px;
    }

    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const playButton = document.getElementById('playButton');
    const gameIframe = document.getElementById('gameIframe');
    const loadingScreen = document.getElementById('loadingScreen');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const gamePlaceholder = document.getElementById('gamePlaceholder');

    playButton.addEventListener('click', function() {
        // Hide play button and show loading
        gamePlaceholder.style.display = 'none';
        loadingScreen.style.display = 'flex';

        // Show iframe after loading delay
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            gameIframe.style.display = 'block';
            fullscreenBtn.style.display = 'block';
        }, 2000);
    });

    fullscreenBtn.addEventListener('click', function() {
        if (gameIframe.requestFullscreen) {
            gameIframe.requestFullscreen();
        } else if (gameIframe.webkitRequestFullscreen) {
            gameIframe.webkitRequestFullscreen();
        } else if (gameIframe.msRequestFullscreen) {
            gameIframe.msRequestFullscreen();
        }
    });
});
</script>
@endsection