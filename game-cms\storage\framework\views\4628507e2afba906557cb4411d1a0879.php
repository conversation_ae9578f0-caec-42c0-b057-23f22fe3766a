<?php $__env->startSection('title', 'Geometry Dash Full Version - Play HTML5 Games'); ?>

<?php $__env->startSection('meta_description', 'Play Geometry Dash Full Version and other free HTML5 games. Enjoy the best online gaming experience with optimized games for all devices.'); ?>

<?php $__env->startSection('schema_markup'); ?>
<script type="application/ld+json">
{
    "<?php $__contextArgs = [];
if (context()->has($__contextArgs[0])) :
if (isset($value)) { $__contextPrevious[] = $value; }
$value = context()->get($__contextArgs[0]); ?>": "https://schema.org",
    "@type": "WebSite",
    "name": "<?php echo e(App\Models\Setting::get('site_name', 'Geometry Dash Full Version')); ?>",
    "url": "<?php echo e(url('/')); ?>",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "<?php echo e(url('/search?q={search_term_string}')); ?>",
        "query-input": "required name=search_term_string"
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Featured Game Section -->
    <div class="mb-12">
        <h1 class="text-3xl font-bold mb-6 text-center">Geometry Dash Full Version</h1>
        
        <div class="flex flex-col lg:flex-row">
            <!-- Game Frame Column -->
            <div class="w-full lg:w-2/3 px-4 mb-8 lg:mb-0">
                <div class="game-frame bg-white p-4 flex flex-col items-center justify-center">
                    <?php if($featuredGame): ?>
                        <?php if($featuredGame->avatar): ?>
                            <img src="<?php echo e(asset('storage/' . $featuredGame->avatar)); ?>" alt="<?php echo e($featuredGame->name); ?>" 
                                 class="w-full h-auto mb-4 rounded">
                        <?php else: ?>
                            <div class="w-full h-48 bg-blue-500 rounded mb-4 flex items-center justify-center text-white text-lg font-bold">
                                <?php echo e($featuredGame->name); ?>

                            </div>
                        <?php endif; ?>
                             
                        <div id="gameControls" class="text-center">
                            <button id="playButton" class="play-button mb-4">
                                Play Geometry Dash Full Version
                            </button>
                            
                            <div id="loadingSpinner" class="loading-spinner hidden"></div>
                            
                            <iframe id="gameIframe" src="about:blank" 
                                    class="w-full h-96 hidden" 
                                    frameborder="0" allowfullscreen></iframe>
                            
                            <button id="fullscreenButton" class="bg-gray-700 text-white px-4 py-2 rounded mt-4 hidden">
                                Fullscreen
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <p class="text-lg">Featured game not available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Popular Games Sidebar -->
            <div class="w-full lg:w-1/3 px-4">
                <div class="bg-white p-4 rounded shadow">
                    <h2 class="text-xl font-bold mb-4">Hot Games</h2>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <?php $__currentLoopData = $popularGames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('games.show', $game)); ?>" class="block group">
                                <div class="relative overflow-hidden rounded">
                                    <?php if($game->avatar): ?>
                                        <img src="<?php echo e(asset('storage/' . $game->avatar)); ?>" alt="<?php echo e($game->name); ?>"
                                             class="w-full h-auto transition-transform group-hover:scale-105">
                                    <?php else: ?>
                                        <div class="w-full h-24 bg-blue-500 flex items-center justify-center text-white text-xs font-bold">
                                            <?php echo e(Str::limit($game->name, 15)); ?>

                                        </div>
                                    <?php endif; ?>

                                    <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2 text-sm">
                                        <?php echo e(Str::limit($game->name, 20)); ?>

                                    </div>
                                </div>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Game Description and How to Play -->
    <div class="mb-12 flex flex-col lg:flex-row">
        <!-- Game Description -->
        <div class="w-full lg:w-2/3 px-4 mb-8 lg:mb-0">
            <div class="bg-white p-6 rounded shadow">
                <h2 class="text-2xl font-bold mb-4">About Geometry Dash Full Version</h2>
                
                <?php if($featuredGame): ?>
                    <div class="mb-4">
                        <div class="flex flex-wrap mb-2">
                            <?php $__currentLoopData = $featuredGame->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e(route('categories.show', $category)); ?>" 
                                   class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm mr-2 mb-2">
                                    <?php echo e($category->name); ?>

                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <div class="flex items-center mb-4">
                            <div class="flex">
                                <?php for($i = 0; $i < 5; $i++): ?>
                                    <svg class="w-5 h-5 <?php echo e($i < 4 ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                <?php endfor; ?>
                            </div>
                            <span class="text-gray-600 ml-2">4.0 out of 5</span>
                        </div>
                        
                        <p class="text-gray-700">
                            <?php echo e($featuredGame->description ?? 'Geometry Dash is a rhythm-based platformer game where you control a cube that automatically runs, jumping over obstacles and avoiding walls. The goal is to reach the end of each level without crashing. The game features amazing music, colorful graphics, and increasingly challenging gameplay that will test your reflexes and timing skills.'); ?>

                        </p>
                    </div>
                <?php else: ?>
                    <p class="text-gray-700">
                        Geometry Dash is a rhythm-based platformer game where you control a cube that automatically runs, jumping over obstacles and avoiding walls. The goal is to reach the end of each level without crashing. The game features amazing music, colorful graphics, and increasingly challenging gameplay that will test your reflexes and timing skills.
                    </p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- How to Play -->
        <div class="w-full lg:w-1/3 px-4">
            <div class="bg-white p-6 rounded shadow">
                <h2 class="text-2xl font-bold mb-4">How to Play</h2>
                
                <ul class="list-disc pl-5 space-y-2 text-gray-700">
                    <li>Press the screen or click your mouse to jump</li>
                    <li>Time your jumps to avoid obstacles</li>
                    <li>Hit jump pads to perform automatic jumps</li>
                    <li>Collect coins for bonus points</li>
                    <li>Each level increases in difficulty</li>
                    <li>Practice makes perfect!</li>
                </ul>
                
                <div class="mt-6">
                    <h3 class="font-bold mb-2">Controls:</h3>
                    <p class="text-gray-700">
                        <span class="font-medium">Mouse:</span> Click to jump<br>
                        <span class="font-medium">Keyboard:</span> Space bar or Up arrow to jump<br>
                        <span class="font-medium">Mobile:</span> Tap the screen to jump
                    </p>
                </div>
                
                <!-- Comments Section -->
                <div class="mt-6">
                    <h3 class="font-bold mb-2">Comments:</h3>
                    
                    <div class="bg-gray-100 p-3 rounded">
                        <p class="text-sm text-gray-700">Comments coming soon...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- New Games Section -->
    <div class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Recently Released Games</h2>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
            <?php $__currentLoopData = $newGames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $game): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('games.show', $game)); ?>" class="block group">
                    <div class="bg-white rounded shadow overflow-hidden">
                        <div class="relative overflow-hidden">
                            <?php if($game->avatar): ?>
                                <img src="<?php echo e(asset('storage/' . $game->avatar)); ?>" alt="<?php echo e($game->name); ?>" 
                                     class="w-full h-auto transition-transform group-hover:scale-105">
                            <?php else: ?>
                                <div class="w-full h-32 bg-blue-500 flex items-center justify-center text-white text-sm font-bold">
                                    <?php echo e(Str::limit($game->name, 15)); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="p-3">
                            <h3 class="font-semibold text-sm group-hover:text-blue-600 line-clamp-2">
                                <?php echo e($game->name); ?>

                            </h3>
                            
                            <div class="flex items-center mt-2">
                                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-xs text-gray-600 ml-1">4.0</span>
                                
                                <span class="text-xs text-gray-500 ml-auto">
                                    <?php echo e($game->play_count); ?> plays
                                </span>
                            </div>
                        </div>
                    </div>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo e(route('games.index')); ?>" class="inline-block bg-blue-600 text-white px-6 py-2 rounded font-semibold hover:bg-blue-700">
                View All Games
            </a>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const playButton = document.getElementById('playButton');
        const gameIframe = document.getElementById('gameIframe');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const fullscreenButton = document.getElementById('fullscreenButton');
        
        if (playButton && gameIframe && loadingSpinner && fullscreenButton) {
            playButton.addEventListener('click', function() {
                // Hide play button and show loading spinner
                playButton.classList.add('hidden');
                loadingSpinner.classList.remove('hidden');
                
                // Set the iframe source (use the featured game's iframe URL or a default)
                <?php if($featuredGame && $featuredGame->iframe_url): ?>
                    gameIframe.src = "<?php echo e($featuredGame->iframe_url); ?>";
                <?php else: ?>
                    gameIframe.src = "https://www.example.com/geometry-dash-embed";
                <?php endif; ?>
                
                // When iframe loads, hide spinner and show iframe and fullscreen button
                gameIframe.onload = function() {
                    loadingSpinner.classList.add('hidden');
                    gameIframe.classList.remove('hidden');
                    fullscreenButton.classList.remove('hidden');
                };
            });
            
            // Fullscreen button functionality
            fullscreenButton.addEventListener('click', function() {
                if (gameIframe.requestFullscreen) {
                    gameIframe.requestFullscreen();
                } else if (gameIframe.mozRequestFullScreen) {
                    gameIframe.mozRequestFullScreen();
                } else if (gameIframe.webkitRequestFullscreen) {
                    gameIframe.webkitRequestFullscreen();
                } else if (gameIframe.msRequestFullscreen) {
                    gameIframe.msRequestFullscreen();
                }
            });
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\cms-theme2\game-cms\resources\views/home.blade.php ENDPATH**/ ?>